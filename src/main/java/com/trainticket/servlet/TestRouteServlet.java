package com.trainticket.servlet;

import com.trainticket.model.Route;
import com.trainticket.service.RouteService;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

@WebServlet(name = "TestRouteServlet", urlPatterns = {"/test-routes"})
public class TestRouteServlet extends HttpServlet {

    private RouteService routeService;

    @Override
    public void init() throws ServletException {
        routeService = new RouteService();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        try {
            out.println("<!DOCTYPE html>");
            out.println("<html>");
            out.println("<head>");
            out.println("<title>Test des Routes</title>");
            out.println("<meta charset='UTF-8'>");
            out.println("<style>");
            out.println("body { font-family: Arial, sans-serif; margin: 20px; }");
            out.println("table { border-collapse: collapse; width: 100%; }");
            out.println("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
            out.println("th { background-color: #f2f2f2; }");
            out.println(".error { color: red; }");
            out.println(".success { color: green; }");
            out.println("</style>");
            out.println("</head>");
            out.println("<body>");
            out.println("<h1>Test de récupération des trajets</h1>");
            
            // Test 1: Récupérer tous les trajets avec JPA
            out.println("<h2>1. Tous les trajets avec JPA :</h2>");
            try {
                List<Route> allRoutes = routeService.getAllRoutesSimple();
                if (allRoutes != null && !allRoutes.isEmpty()) {
                    out.println("<p class='success'>✅ " + allRoutes.size() + " trajet(s) trouvé(s)</p>");
                    out.println("<table>");
                    out.println("<tr>");
                    out.println("<th>ID</th>");
                    out.println("<th>Ville de départ</th>");
                    out.println("<th>Ville d'arrivée</th>");
                    out.println("<th>Heure de départ</th>");
                    out.println("<th>Heure d'arrivée</th>");
                    out.println("<th>Prix</th>");
                    out.println("<th>Places disponibles</th>");
                    out.println("<th>Train ID</th>");
                    out.println("</tr>");
                    
                    for (Route route : allRoutes) {
                        out.println("<tr>");
                        out.println("<td>" + route.getId() + "</td>");
                        out.println("<td>" + (route.getDepartureCity() != null ? route.getDepartureCity() : "N/A") + "</td>");
                        out.println("<td>" + (route.getArrivalCity() != null ? route.getArrivalCity() : "N/A") + "</td>");
                        out.println("<td>" + (route.getDepartureTime() != null ? route.getDepartureTime() : "N/A") + "</td>");
                        out.println("<td>" + (route.getArrivalTime() != null ? route.getArrivalTime() : "N/A") + "</td>");
                        out.println("<td>" + (route.getPrice() != null ? route.getPrice() + " €" : "N/A") + "</td>");
                        out.println("<td>" + (route.getAvailableSeats() != null ? route.getAvailableSeats() : "N/A") + "</td>");
                        out.println("<td>" + (route.getTrain() != null ? route.getTrain().getId() : "N/A") + "</td>");
                        out.println("</tr>");
                    }
                    out.println("</table>");
                } else {
                    out.println("<p class='error'>❌ Aucun trajet trouvé dans la base de données</p>");
                }
            } catch (Exception e) {
                out.println("<p class='error'>❌ Erreur lors de la récupération des trajets : " + e.getMessage() + "</p>");
                e.printStackTrace();
            }
            
            // Test 2: Test de recherche simple
            out.println("<h2>2. Test de recherche Paris -> Lyon :</h2>");
            try {
                List<Route> searchResults = routeService.searchRoutes("Paris", "Lyon");
                if (searchResults != null && !searchResults.isEmpty()) {
                    out.println("<p class='success'>✅ " + searchResults.size() + " trajet(s) trouvé(s) pour Paris -> Lyon</p>");
                } else {
                    out.println("<p class='error'>❌ Aucun trajet trouvé pour Paris -> Lyon</p>");
                }
            } catch (Exception e) {
                out.println("<p class='error'>❌ Erreur lors de la recherche Paris -> Lyon : " + e.getMessage() + "</p>");
            }
            
            // Test 3: Test de recherche avec des villes de votre base
            out.println("<h2>3. Test de recherche avec vos données :</h2>");
            try {
                // Essayons avec les villes que je vois dans votre base
                List<Route> searchResults = routeService.searchRoutes("Paris", "Paris");
                out.println("<p>Recherche Paris -> Paris : " + (searchResults != null ? searchResults.size() : 0) + " résultat(s)</p>");
                
                searchResults = routeService.searchRoutes("Lyon", "Lyon");
                out.println("<p>Recherche Lyon -> Lyon : " + (searchResults != null ? searchResults.size() : 0) + " résultat(s)</p>");
                
            } catch (Exception e) {
                out.println("<p class='error'>❌ Erreur lors des tests de recherche : " + e.getMessage() + "</p>");
            }
            
            out.println("<br><a href='/search'>← Retour à la page de recherche</a>");
            out.println("</body>");
            out.println("</html>");
            
        } catch (Exception e) {
            out.println("<h1>Erreur générale</h1>");
            out.println("<p class='error'>" + e.getMessage() + "</p>");
            e.printStackTrace();
        } finally {
            out.close();
        }
    }
}
