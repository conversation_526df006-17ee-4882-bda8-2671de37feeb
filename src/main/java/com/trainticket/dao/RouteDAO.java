package com.trainticket.dao;

import com.trainticket.model.Route;
import com.trainticket.model.Train;

import java.time.LocalDateTime;
import java.util.List;

public interface RouteDAO extends GenericDAO<Route, Long> {

    /**
     * Recherche des trajets par ville de départ et d'arrivée
     */
    List<Route> findByDepartureAndArrivalCities(String departureCity, String arrivalCity);

    /**
     * Recherche des trajets par ville de départ, d'arrivée et date
     */
    List<Route> findByDepartureAndArrivalCitiesAndDate(String departureCity, String arrivalCity,
                                                       LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Trouve tous les trajets d'un train spécifique
     */
    List<Route> findByTrain(Train train);

    /**
     * Trouve tous les trajets actifs
     */
    List<Route> findActiveRoutes();

    /**
     * Trouve les trajets avec des places disponibles
     */
    List<Route> findRoutesWithAvailableSeats();

    /**
     * Trouve les trajets par ville de départ
     */
    List<Route> findByDepartureCity(String departureCity);

    /**
     * Trouve les trajets par ville d'arrivée
     */
    List<Route> findByArrivalCity(String arrivalCity);

    /**
     * Trouve les trajets futurs (après maintenant)
     */
    List<Route> findFutureRoutes();

    /**
     * Trouve les trajets dans une plage de dates
     */
    List<Route> findByDateRange(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Trouve toutes les villes de départ distinctes
     */
    List<String> findDistinctDepartureCities();

    /**
     * Trouve toutes les villes d'arrivée distinctes
     */
    List<String> findDistinctArrivalCities();

    /**
     * Compte le nombre de trajets actifs
     */
    long countActiveRoutes();

    /**
     * Recherche dynamique avec requête SQL native
     */
    List<Route> findByDynamicSearch(String departureCity, String arrivalCity,
                                   String date, String time);

    /**
     * Récupère tous les trajets avec requête SQL native
     */
    List<Route> findAllNative();
}
