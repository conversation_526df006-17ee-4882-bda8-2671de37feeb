package com.trainticket.dao.impl;

import com.trainticket.dao.RouteDAO;
import com.trainticket.model.Route;
import com.trainticket.model.Train;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class RouteDAOImpl extends GenericDAOImpl<Route, Long> implements RouteDAO {

    @Override
    public List<Route> findByDepartureAndArrivalCities(String departureCity, String arrivalCity) {
        EntityManager em = getEntityManager();
        try {
            // Recherche simple sans filtre isActive pour commencer
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE LOWER(r.departureCity) LIKE LOWER(:departureCity) " +
                "AND LOWER(r.arrivalCity) LIKE LOWER(:arrivalCity) " +
                "ORDER BY r.departureTime", Route.class);
            query.setParameter("departureCity", "%" + departureCity + "%");
            query.setParameter("arrivalCity", "%" + arrivalCity + "%");
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findByDepartureAndArrivalCitiesAndDate(String departureCity, String arrivalCity,
                                                             LocalDateTime startDate, LocalDateTime endDate) {
        EntityManager em = getEntityManager();
        try {
            // Recherche simplifiée avec LIKE pour plus de flexibilité
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE LOWER(r.departureCity) LIKE LOWER(:departureCity) " +
                "AND LOWER(r.arrivalCity) LIKE LOWER(:arrivalCity) " +
                "AND r.departureTime >= :startDate AND r.departureTime <= :endDate " +
                "ORDER BY r.departureTime", Route.class);
            query.setParameter("departureCity", "%" + departureCity + "%");
            query.setParameter("arrivalCity", "%" + arrivalCity + "%");
            query.setParameter("startDate", startDate);
            query.setParameter("endDate", endDate);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findByTrain(Train train) {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE r.train = :train ORDER BY r.departureTime", Route.class);
            query.setParameter("train", train);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findActiveRoutes() {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE r.isActive = true ORDER BY r.departureTime", Route.class);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findRoutesWithAvailableSeats() {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE r.isActive = true AND r.availableSeats > 0 " +
                "ORDER BY r.departureTime", Route.class);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findByDepartureCity(String departureCity) {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE LOWER(r.departureCity) LIKE LOWER(:departureCity) " +
                "ORDER BY r.departureTime", Route.class);
            query.setParameter("departureCity", "%" + departureCity + "%");
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findByArrivalCity(String arrivalCity) {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE LOWER(r.arrivalCity) LIKE LOWER(:arrivalCity) " +
                "ORDER BY r.departureTime", Route.class);
            query.setParameter("arrivalCity", "%" + arrivalCity + "%");
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findFutureRoutes() {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE r.departureTime > :now AND r.isActive = true " +
                "ORDER BY r.departureTime", Route.class);
            query.setParameter("now", LocalDateTime.now());
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Route> query = em.createQuery(
                "SELECT r FROM Route r WHERE r.departureTime >= :startDate " +
                "AND r.departureTime <= :endDate AND r.isActive = true " +
                "ORDER BY r.departureTime", Route.class);
            query.setParameter("startDate", startDate);
            query.setParameter("endDate", endDate);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<String> findDistinctDepartureCities() {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<String> query = em.createQuery(
                "SELECT DISTINCT r.departureCity FROM Route r WHERE r.isActive = true " +
                "ORDER BY r.departureCity", String.class);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public List<String> findDistinctArrivalCities() {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<String> query = em.createQuery(
                "SELECT DISTINCT r.arrivalCity FROM Route r WHERE r.isActive = true " +
                "ORDER BY r.arrivalCity", String.class);
            return query.getResultList();
        } finally {
            em.close();
        }
    }

    @Override
    public long countActiveRoutes() {
        EntityManager em = getEntityManager();
        try {
            TypedQuery<Long> query = em.createQuery(
                "SELECT COUNT(r) FROM Route r WHERE r.isActive = true", Long.class);
            return query.getSingleResult();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findAllNative() {
        EntityManager em = getEntityManager();
        try {
            // Requête SQL native qui s'adapte à votre table réelle
            String sql = "SELECT * FROM trajet ORDER BY departure_time";

            @SuppressWarnings("unchecked")
            List<Object[]> results = em.createNativeQuery(sql).getResultList();

            List<Route> routes = new ArrayList<>();
            for (Object[] row : results) {
                Route route = mapRowToRoute(row);
                if (route != null) {
                    routes.add(route);
                }
            }
            return routes;
        } catch (Exception e) {
            System.err.println("Erreur dans findAllNative: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        } finally {
            em.close();
        }
    }

    @Override
    public List<Route> findByDynamicSearch(String departureCity, String arrivalCity,
                                          String date, String time) {
        EntityManager em = getEntityManager();
        try {
            StringBuilder sql = new StringBuilder("SELECT * FROM trajet WHERE 1=1");

            // Construire la requête dynamiquement
            if (departureCity != null && !departureCity.trim().isEmpty()) {
                sql.append(" AND LOWER(departure_city) LIKE LOWER(?)");
            }
            if (arrivalCity != null && !arrivalCity.trim().isEmpty()) {
                sql.append(" AND LOWER(arrival_city) LIKE LOWER(?)");
            }
            if (date != null && !date.trim().isEmpty()) {
                sql.append(" AND DATE(departure_time) = ?");
            }

            sql.append(" ORDER BY departure_time");

            jakarta.persistence.Query query = em.createNativeQuery(sql.toString());

            // Définir les paramètres dynamiquement
            int paramIndex = 1;
            if (departureCity != null && !departureCity.trim().isEmpty()) {
                query.setParameter(paramIndex++, "%" + departureCity.trim() + "%");
            }
            if (arrivalCity != null && !arrivalCity.trim().isEmpty()) {
                query.setParameter(paramIndex++, "%" + arrivalCity.trim() + "%");
            }
            if (date != null && !date.trim().isEmpty()) {
                query.setParameter(paramIndex++, date);
            }

            @SuppressWarnings("unchecked")
            List<Object[]> results = query.getResultList();

            List<Route> routes = new ArrayList<>();
            for (Object[] row : results) {
                Route route = mapRowToRoute(row);
                if (route != null) {
                    routes.add(route);
                }
            }
            return routes;
        } catch (Exception e) {
            System.err.println("Erreur dans findByDynamicSearch: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        } finally {
            em.close();
        }
    }

    /**
     * Mappe une ligne de résultat SQL vers un objet Route
     */
    private Route mapRowToRoute(Object[] row) {
        try {
            Route route = new Route();

            // Mapping basé sur l'ordre des colonnes dans votre table
            // Ajustez selon votre structure réelle
            int i = 0;

            // ID
            if (row[i] != null) {
                route.setId(((Number) row[i]).longValue());
            }
            i++;

            // departure_city
            if (row[i] != null) {
                route.setDepartureCity(row[i].toString());
            }
            i++;

            // arrival_city
            if (row[i] != null) {
                route.setArrivalCity(row[i].toString());
            }
            i++;

            // departure_time
            if (row[i] != null) {
                if (row[i] instanceof java.sql.Timestamp) {
                    route.setDepartureTime(((java.sql.Timestamp) row[i]).toLocalDateTime());
                }
            }
            i++;

            // arrival_time
            if (row[i] != null) {
                if (row[i] instanceof java.sql.Timestamp) {
                    route.setArrivalTime(((java.sql.Timestamp) row[i]).toLocalDateTime());
                }
            }
            i++;

            // price
            if (row[i] != null) {
                route.setPrice(new java.math.BigDecimal(row[i].toString()));
            }
            i++;

            // available_seats
            if (i < row.length && row[i] != null) {
                route.setAvailableSeats(((Number) row[i]).intValue());
            }
            i++;

            // train_id - nous pouvons l'ignorer pour l'instant ou créer un train basique
            if (i < row.length && row[i] != null) {
                // Pour l'instant, on peut créer un train basique ou l'ignorer
                // route.setTrain(...);
            }

            return route;
        } catch (Exception e) {
            System.err.println("Erreur lors du mapping de la ligne: " + e.getMessage());
            return null;
        }
    }
}
